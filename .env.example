# Telegram Bot Configuration
BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
BOT_USERNAME=your_marketplace_bot

# Web App Configuration
WEB_APP_URL=https://your-domain.com/

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration (if needed)
DATABASE_URL=postgresql://user:password@localhost:5432/marketplace_bot

# Firebase Configuration (if integrating with marketplace)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Webhook Configuration (for production)
WEBHOOK_URL=https://your-domain.com/webhook
WEBHOOK_SECRET=your_random_secret_string

# Logging
LOG_LEVEL=debug
