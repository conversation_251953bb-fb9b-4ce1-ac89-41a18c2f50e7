{"name": "marketplace-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@telegram-apps/sdk-react": "^3.2.4", "@ton/core": "^0.61.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.2.1", "@tonconnect/ui-react": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "firebase": "^10.14.1", "firebaseui": "^6.1.0", "lucide-react": "^0.516.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}