import { TelegramTest } from "@/components/telegram-test";

export default function TelegramTestPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8 text-center">
        Telegram Web App Integration Test
      </h1>

      <div className="max-w-4xl mx-auto">
        <TelegramTest />

        <div className="mt-8 p-6 border rounded-lg bg-blue-50">
          <h2 className="text-xl font-semibold mb-4">How to Test</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>
              <strong>From Telegram Desktop/Mobile:</strong> Open this page
              through a Telegram bot or mini app
            </li>
            <li>
              <strong>Expected Results:</strong>
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>
                  &quot;Is in Telegram Web App&quot; should show &quot;Yes&quot;
                </li>
                <li>&quot;Is Initialized&quot; should show &quot;Yes&quot;</li>
                <li>Telegram Data should display user information</li>
                <li>
                  Telegram WebApp Object should show initData and initDataUnsafe
                </li>
              </ul>
            </li>
            <li>
              <strong>If not working:</strong>
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>Check browser console for error messages</li>
                <li>Ensure you&apos;re opening from within Telegram</li>
                <li>Try the retry button if there are initialization errors</li>
              </ul>
            </li>
          </ol>
        </div>
      </div>
    </div>
  );
}
