import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import { RootProvider } from "@/root-context";
import TonLayout from "./ton-layout";
import { Toaster } from "sonner";
import { TelegramInitializer } from "@/components/telegram-initializer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head></head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Script
          src="https://telegram.org/js/telegram-web-app.js"
          strategy="beforeInteractive"
        />
        <RootProvider>
          <TelegramInitializer />
          <TonLayout>{children}</TonLayout>
          <Toaster />
        </RootProvider>
      </body>
    </html>
  );
}
