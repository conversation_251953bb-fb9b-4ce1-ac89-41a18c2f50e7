"use client";

import { initTelegramSDK } from "./telegram-client";

let isInitialized = false;

/**
 * Initialize Telegram SDK when the app loads
 * This should be called once when the app starts
 */
export async function initializeTelegramApp(): Promise<void> {
  if (isInitialized || typeof window === "undefined") {
    return;
  }

  try {
    console.log("🚀 Initializing Telegram Mini App...");
    
    const success = await initTelegramSDK();
    
    if (success) {
      console.log("✅ Telegram Mini App initialized successfully");
    } else {
      console.log("⚠️ Telegram Mini App initialization failed or not in Telegram environment");
    }
    
    isInitialized = true;
  } catch (error) {
    console.error("❌ Failed to initialize Telegram Mini App:", error);
    isInitialized = true; // Prevent retries
  }
}

/**
 * Check if the app has been initialized
 */
export function isTelegramAppInitialized(): boolean {
  return isInitialized;
}
