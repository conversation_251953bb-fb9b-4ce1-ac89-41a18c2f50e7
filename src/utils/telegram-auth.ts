import CryptoJS from "crypto-js";
import {
  getTelegramLaunchParams,
  getTelegramInitData,
  initTelegramSDK,
} from "./telegram-client";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface TelegramWebAppInitData {
  user?: TelegramUser;
  chat_instance?: string;
  chat_type?: string;
  auth_date: number;
  hash: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

/**
 * Validates Telegram Web App init data using the bot token
 * Based on Telegram's validation algorithm
 */
export function validateTelegramWebAppData(
  initData: string,
  botToken: string
): ValidationResult {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort((a, b) =>
      a[0].localeCompare(b[0])
    )) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

/**
 * Extracts user information from Telegram Web App using @telegram-apps/sdk
 * This should be called on the client side
 */
export async function getTelegramWebAppData(): Promise<TelegramWebAppInitData | null> {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    console.log(
      "=== Getting Telegram WebApp Data using @telegram-apps/sdk ==="
    );

    let initData = "";

    // Primary method: Use @telegram-apps/sdk via client utility
    try {
      console.log("Trying @telegram-apps/sdk...");
      initData = getTelegramInitData();
      if (initData) {
        console.log("Using @telegram-apps/sdk initData:", initData);
      }
    } catch (error) {
      console.warn("@telegram-apps/sdk failed:", error);
    }

    // Fallback method: Use window.Telegram.WebApp directly
    if (!initData) {
      try {
        console.log("Trying window.Telegram.WebApp fallback...");
        const telegramWebApp = window.Telegram?.WebApp;

        if (telegramWebApp) {
          console.log("Found window.Telegram.WebApp:", telegramWebApp);
          console.log("- initData:", telegramWebApp.initData);
          console.log("- initDataUnsafe:", telegramWebApp.initDataUnsafe);
          console.log("- platform:", telegramWebApp.platform);
          console.log("- version:", telegramWebApp.version);

          // Use initData (raw string) for authentication
          initData = telegramWebApp.initData || "";
          console.log("Using window.Telegram.WebApp.initData:", initData);

          // If initData is still empty, try to construct it from initDataUnsafe
          if (!initData && telegramWebApp.initDataUnsafe) {
            console.log(
              "initData is empty, trying to construct from initDataUnsafe..."
            );
            try {
              const unsafeData = telegramWebApp.initDataUnsafe;
              console.log("initDataUnsafe content:", unsafeData);

              // Construct initData from initDataUnsafe
              const params = new URLSearchParams();

              if (unsafeData.user) {
                params.set("user", JSON.stringify(unsafeData.user));
              }
              if (unsafeData.chat_instance) {
                params.set("chat_instance", unsafeData.chat_instance);
              }
              if (unsafeData.chat_type) {
                params.set("chat_type", unsafeData.chat_type);
              }
              if (unsafeData.auth_date) {
                params.set("auth_date", unsafeData.auth_date.toString());
              }
              if ("start_param" in unsafeData && unsafeData.start_param) {
                params.set("start_param", String(unsafeData.start_param));
              }

              // Add a placeholder hash (this won't be valid for server-side validation)
              params.set("hash", "unsafe_fallback_hash");

              initData = params.toString();
              console.log(
                "Constructed initData from initDataUnsafe:",
                initData
              );
            } catch (constructError) {
              console.warn(
                "Failed to construct initData from initDataUnsafe:",
                constructError
              );
            }
          }
        }
      } catch (error) {
        console.warn("window.Telegram.WebApp access failed:", error);
      }
    }

    // Additional fallback: Try @telegram-apps/sdk launch params if initData is still empty
    if (!initData) {
      try {
        console.log("Trying @telegram-apps/sdk launch params fallback...");
        const launchParams = getTelegramLaunchParams();

        if (
          launchParams &&
          typeof launchParams === "object" &&
          "initData" in launchParams
        ) {
          const initDataObj = launchParams.initData;
          console.log("@telegram-apps/sdk initData object:", initDataObj);

          if (initDataObj && typeof initDataObj === "object") {
            // Construct initData from the parsed object
            const params = new URLSearchParams();

            if ("user" in initDataObj && initDataObj.user) {
              params.set("user", JSON.stringify(initDataObj.user));
            }
            if ("chatInstance" in initDataObj && initDataObj.chatInstance) {
              params.set("chat_instance", String(initDataObj.chatInstance));
            }
            if ("chatType" in initDataObj && initDataObj.chatType) {
              params.set("chat_type", String(initDataObj.chatType));
            }
            if ("authDate" in initDataObj && initDataObj.authDate) {
              params.set("auth_date", String(initDataObj.authDate));
            }
            if ("startParam" in initDataObj && initDataObj.startParam) {
              params.set("start_param", String(initDataObj.startParam));
            }

            // Add a placeholder hash (this won't be valid for server-side validation)
            params.set("hash", "telegram_apps_sdk_fallback_hash");

            initData = params.toString();
            console.log(
              "Constructed initData from @telegram-apps/sdk initData object:",
              initData
            );
          }
        }
      } catch (sdkError) {
        console.warn(
          "@telegram-apps/sdk initData object access failed:",
          sdkError
        );
      }
    }

    // Fallback method: Check URL parameters for tgWebAppData
    if (!initData) {
      console.log("No SDK initData found, checking URL parameters...");
      const urlParams = new URLSearchParams(window.location.search);
      const tgWebAppData =
        urlParams.get("tgWebAppData") ?? urlParams.get("initData");
      if (tgWebAppData) {
        console.log("Found tgWebAppData in URL:", tgWebAppData);
        initData = tgWebAppData;
      }
    }

    // Another fallback: Check if data is in hash fragment
    if (!initData && window.location.hash) {
      console.log("Checking hash fragment for Telegram data...");
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const hashInitData =
        hashParams.get("tgWebAppData") ?? hashParams.get("initData");
      if (hashInitData) {
        console.log("Found initData in hash:", hashInitData);
        initData = hashInitData;
      }
    }

    console.log("Final initData result:", initData);

    if (!initData) {
      console.warn("Telegram WebApp initData is empty");
      console.warn("Current URL:", window.location.href);
      console.warn("URL search params:", window.location.search);
      console.warn("URL hash:", window.location.hash);

      // For development/testing, check if we should create mock data
      if (
        process.env.NODE_ENV === "development" ||
        window.location.hostname === "localhost"
      ) {
        console.log(
          "Development mode detected - you can use the mock authentication component"
        );
      }

      return null;
    }

    // Parse init data from URL parameters
    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");
    const authDate = urlParams.get("auth_date");
    const hash = urlParams.get("hash");

    console.log("Parsed URL params:", {
      userParam: userParam ? "present" : "missing",
      authDate: authDate ? "present" : "missing",
      hash: hash ? "present" : "missing",
    });

    if (!userParam || !authDate || !hash) {
      console.warn("Missing required Telegram data fields:", {
        userParam: !!userParam,
        authDate: !!authDate,
        hash: !!hash,
        rawInitData: initData,
      });
      return null;
    }

    const user: TelegramUser = JSON.parse(userParam);

    return {
      user,
      chat_instance: urlParams.get("chat_instance") ?? undefined,
      chat_type: urlParams.get("chat_type") ?? undefined,
      auth_date: parseInt(authDate, 10),
      hash,
    };
  } catch (error) {
    console.error("Error getting Telegram Web App data:", error);
    return null;
  }
}

export async function initTelegramWebApp(): Promise<boolean> {
  if (typeof window === "undefined") {
    return false;
  }

  // Use the client-side utility for initialization
  const sdkInitialized = await initTelegramSDK();

  if (sdkInitialized) {
    return true;
  }

  // Fallback to window.Telegram.WebApp if available
  try {
    const telegramWebApp = window.Telegram?.WebApp;
    if (telegramWebApp) {
      telegramWebApp.ready();
      telegramWebApp.expand();
      console.log("Fallback: Initialized with window.Telegram.WebApp");
      return true;
    }
  } catch (fallbackError) {
    console.error("Fallback initialization also failed:", fallbackError);
  }

  return false;
}

/**
 * Checks if the app is running inside Telegram using @telegram-apps/sdk and fallbacks
 */
export async function isTelegramWebApp(): Promise<boolean> {
  if (typeof window === "undefined") {
    return false;
  }

  try {
    // Primary method: Check @telegram-apps/sdk
    try {
      const launchParams = getTelegramLaunchParams();
      if (launchParams && typeof launchParams === "object") {
        return true;
      }
    } catch {
      // Ignore error and try fallback
    }

    // Fallback: Check window.Telegram.WebApp
    const telegramWebApp = window.Telegram?.WebApp;
    if (telegramWebApp?.initData !== undefined) {
      return true;
    }

    // No additional fallbacks needed - @telegram-apps/sdk and window.Telegram.WebApp should cover all cases
    return false;
  } catch (error) {
    console.warn("Error checking Telegram WebApp availability:", error);
    return false;
  }
}
