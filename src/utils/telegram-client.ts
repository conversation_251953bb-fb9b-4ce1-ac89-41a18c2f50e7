"use client";

import {
  setDebug,
  init as initSDK,
  restoreInitData,
  retrieveLaunchParams,
  mountMiniAppSync,
  bindThemeParamsCssVars,
  mountViewport,
  bindViewportCssVars,
  mountBackButton,
} from "@telegram-apps/sdk-react";
import { mockEnv } from "./mock-env";

let isInitialized = false;

/**
 * Client-side only Telegram SDK initialization
 * This prevents hydration errors by only running on the client
 */
export async function initTelegramSDK(): Promise<boolean> {
  if (typeof window === "undefined" || isInitialized) {
    return isInitialized;
  }

  try {
    console.log("Initializing Telegram SDK...");

    // Mock environment for development
    await mockEnv();

    // Set debug mode for development
    const debug = process.env.NODE_ENV === "development";
    setDebug(debug);

    // Initialize the SDK
    initSDK();

    // Restore init data
    restoreInitData();

    // Mount components
    mountBackButton.ifAvailable();

    if (mountMiniAppSync.isAvailable()) {
      mountMiniAppSync();
      bindThemeParamsCssVars();
    }

    if (mountViewport.isAvailable()) {
      mountViewport().then(() => {
        bindViewportCssVars();
      });
    }

    isInitialized = true;
    console.log("Telegram SDK initialized successfully");
    return true;
  } catch (error) {
    console.error("Failed to initialize Telegram SDK:", error);
    return false;
  }
}

/**
 * Client-side only function to get launch params
 * This prevents hydration errors and handles cases where app is opened outside Telegram
 */
export function getTelegramLaunchParams() {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    return retrieveLaunchParams();
  } catch (error) {
    // This is expected when app is opened outside Telegram
    console.log(
      "App is not running in Telegram environment:",
      error instanceof Error ? error.message : String(error)
    );
    return null;
  }
}

/**
 * Client-side only function to get Telegram init data
 * Uses tgWebAppData as primary source, falls back to initDataRaw
 */
export function getTelegramInitData(): string {
  if (typeof window === "undefined") {
    return "";
  }

  try {
    const launchParams = getTelegramLaunchParams();

    // First try tgWebAppData if available
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "tgWebAppData" in launchParams &&
      launchParams.tgWebAppData
    ) {
      return String(launchParams.tgWebAppData);
    }

    // Fallback to initDataRaw
    if (
      launchParams &&
      typeof launchParams === "object" &&
      "initDataRaw" in launchParams &&
      launchParams.initDataRaw
    ) {
      return String(launchParams.initDataRaw);
    }

    // Final fallback to window.Telegram.WebApp
    const telegramWebApp = window.Telegram?.WebApp;
    if (telegramWebApp?.initData) {
      return telegramWebApp.initData;
    }

    return "";
  } catch (error) {
    console.warn("Failed to get Telegram init data:", error);
    return "";
  }
}
