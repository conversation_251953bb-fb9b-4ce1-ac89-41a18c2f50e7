"use client";

import { useEffect } from "react";
import { initializeTelegramApp } from "@/utils/telegram-init";

/**
 * Component that initializes Telegram SDK when the app loads
 * This should be included in the root layout
 */
export function TelegramInitializer() {
  useEffect(() => {
    // Initialize Telegram SDK when component mounts
    initializeTelegramApp().catch((error) => {
      console.error("Failed to initialize Telegram app:", error);
    });
  }, []);

  // This component doesn't render anything
  return null;
}
