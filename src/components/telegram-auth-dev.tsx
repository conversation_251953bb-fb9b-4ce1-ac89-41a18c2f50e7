"use client";

import { useState } from "react";
import { signInWithCustomToken } from "firebase/auth";
import { httpsCallable } from "firebase/functions";

import { firebaseAuth, firebaseFunctions } from "@/root-context";

interface TelegramAuthDevProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const TelegramAuthDev = ({
  onSuccess,
  onError,
}: TelegramAuthDevProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showMockData, setShowMockData] = useState(false);

  // Mock Telegram data for development testing
  const createMockTelegramData = () => {
    const mockUser = {
      id: 123456789,
      first_name: "Test",
      last_name: "User",
      username: "testuser",
      language_code: "en",
      is_premium: false,
    };

    const authDate = Math.floor(Date.now() / 1000);
    const userData = JSON.stringify(mockUser);

    // Create mock initData string
    const params = new URLSearchParams();
    params.append("user", userData);
    params.append("auth_date", authDate.toString());
    params.append("chat_instance", "123456789");
    params.append("chat_type", "private");

    // For development, we'll use a mock hash (in production this would be validated)
    params.append("hash", "mock_hash_for_development");

    return params.toString();
  };

  const handleMockTelegramAuth = async () => {
    setIsLoading(true);

    try {
      const mockInitData = createMockTelegramData();

      // Call Firebase function to authenticate with mock data
      const authenticateWithTelegram = httpsCallable(
        firebaseFunctions,
        "authenticateWithTelegram"
      );

      const result = await authenticateWithTelegram({
        initData: mockInitData,
      });

      const { customToken } = result.data as { customToken: string };

      // Sign in with custom token
      await signInWithCustomToken(firebaseAuth, customToken);

      onSuccess?.();
    } catch (error) {
      console.error("Mock Telegram authentication error:", error);

      let errorMessage = "Mock authentication failed";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        errorMessage = String(error.message);
      }

      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="text-center p-4">
      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-blue-800 font-medium mb-2">🧪 Development Mode</p>
        <p className="text-blue-700 text-sm mb-3">
          This is a development component for testing Telegram authentication
          without being in Telegram.
        </p>

        <button
          onClick={() => setShowMockData(!showMockData)}
          className="text-blue-600 underline text-sm mb-2"
        >
          {showMockData ? "Hide" : "Show"} Mock Data
        </button>

        {showMockData && (
          <div className="mt-3 p-3 bg-blue-100 rounded text-left">
            <p className="text-xs text-blue-800 font-medium mb-2">
              Mock Telegram Data:
            </p>
            <pre className="text-xs text-blue-700 overflow-auto">
              {JSON.stringify(
                {
                  user: {
                    id: 123456789,
                    first_name: "Test",
                    last_name: "User",
                    username: "testuser",
                    language_code: "en",
                    is_premium: false,
                  },
                  auth_date: Math.floor(Date.now() / 1000),
                  chat_instance: "123456789",
                  chat_type: "private",
                  hash: "mock_hash_for_development",
                },
                null,
                2
              )}
            </pre>
          </div>
        )}
      </div>

      <button
        onClick={handleMockTelegramAuth}
        disabled={isLoading}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2"
      >
        {isLoading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Testing Authentication...
          </>
        ) : (
          "🧪 Test with Mock Telegram Data"
        )}
      </button>

      <p className="text-xs text-gray-500 mt-2">
        ⚠️ This will only work if the Firebase Functions are configured to
        accept mock data in development mode.
      </p>
    </div>
  );
};

export default TelegramAuthDev;
