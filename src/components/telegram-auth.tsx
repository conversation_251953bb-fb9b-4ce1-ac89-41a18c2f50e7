"use client";

import { useState } from "react";
import { signInWithCustomToken } from "firebase/auth";
import { httpsCallable } from "firebase/functions";

import { firebaseAuth, firebaseFunctions } from "@/root-context";
import { getTelegramWebAppData } from "@/utils/telegram-auth";
import { useTelegramWebApp } from "@/hooks/useTelegramWebApp";
import { getTelegramInitData } from "@/utils/telegram-client";

interface TelegramAuthProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const TelegramAuth = ({ onSuccess, onError }: TelegramAuthProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const {
    isInTelegram,
    isInitialized,
    error: telegramError,
    retry,
  } = useTelegramWebApp();

  const handleTelegramAuth = async () => {
    if (!isInTelegram) {
      onError?.(
        "This app must be opened from Telegram Web App. Please open it from a Telegram bot or mini app."
      );
      return;
    }

    if (!isInitialized) {
      onError?.(
        "Telegram Web App is not properly initialized. Please wait a moment and try again."
      );
      return;
    }

    setIsLoading(true);

    try {
      console.log("[TelegramAuth] Starting authentication...");

      const telegramData = await getTelegramWebAppData();
      console.log("[TelegramAuth] Retrieved telegramData:", telegramData);

      if (!telegramData) {
        console.error("[TelegramAuth] No telegram data available");
        throw new Error(
          "Unable to retrieve Telegram user data. Please ensure you're opening this from a Telegram Web App."
        );
      }

      console.log("[TelegramAuth] Calling Firebase function...");
      const authenticateWithTelegram = httpsCallable(
        firebaseFunctions,
        "authenticateWithTelegram"
      );

      // Try to get initData using the new client utility
      let initDataToSend = "";

      try {
        initDataToSend = getTelegramInitData();
        if (initDataToSend) {
          console.log(
            "[TelegramAuth] Using getTelegramInitData:",
            initDataToSend
          );
        }
      } catch (error) {
        console.warn("[TelegramAuth] getTelegramInitData failed:", error);
      }

      // Fallback to window.Telegram.WebApp
      if (!initDataToSend) {
        try {
          const telegramWebApp = window.Telegram?.WebApp;
          if (telegramWebApp?.initData) {
            initDataToSend = telegramWebApp.initData;
            console.log(
              "[TelegramAuth] Using window.Telegram.WebApp.initData:",
              initDataToSend
            );
          } else if (telegramWebApp?.initDataUnsafe) {
            // Fallback to constructing from initDataUnsafe
            console.log(
              "[TelegramAuth] initData is empty, constructing from initDataUnsafe..."
            );
            try {
              const unsafeData = telegramWebApp.initDataUnsafe;
              const params = new URLSearchParams();

              if (unsafeData.user) {
                params.set("user", JSON.stringify(unsafeData.user));
              }
              if (unsafeData.chat_instance) {
                params.set("chat_instance", unsafeData.chat_instance);
              }
              if (unsafeData.chat_type) {
                params.set("chat_type", unsafeData.chat_type);
              }
              if (unsafeData.auth_date) {
                params.set("auth_date", unsafeData.auth_date.toString());
              }
              if ("start_param" in unsafeData && unsafeData.start_param) {
                params.set("start_param", String(unsafeData.start_param));
              }

              // Add a placeholder hash (this won't be valid for server-side validation)
              params.set("hash", "unsafe_fallback_hash");

              initDataToSend = params.toString();
              console.log(
                "[TelegramAuth] Constructed initData from initDataUnsafe:",
                initDataToSend
              );
            } catch (constructError) {
              console.warn(
                "[TelegramAuth] Failed to construct from initDataUnsafe:",
                constructError
              );
            }
          }
        } catch (error) {
          console.warn("[TelegramAuth] window.Telegram.WebApp failed:", error);
        }
      }

      // If still no initData, log the issue
      if (!initDataToSend) {
        console.warn("[TelegramAuth] No initData available from any source");
      }

      const result = await authenticateWithTelegram({
        initData: initDataToSend,
      });

      console.log("[TelegramAuth] Firebase function result:", result);

      const { customToken } = result.data as { customToken: string };

      await signInWithCustomToken(firebaseAuth, customToken);

      onSuccess?.();
    } catch (error) {
      console.error("Telegram authentication error:", error);

      let errorMessage = "Authentication failed";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        errorMessage = String(error.message);
      }

      // Provide more specific error messages
      if (errorMessage.includes("Failed to get Telegram data")) {
        errorMessage =
          "Unable to access Telegram user data. Please ensure you're opening this app from within Telegram.";
      } else if (errorMessage.includes("initData")) {
        errorMessage =
          "Telegram Web App data is missing. Please try refreshing the app or reopening from Telegram.";
      } else if (
        errorMessage.includes("internal") ||
        errorMessage.includes("INTERNAL")
      ) {
        errorMessage =
          "Server configuration error. The Telegram authentication service needs to be set up. Please contact the administrator.";
      } else if (
        errorMessage.includes("UNAUTHENTICATED") ||
        errorMessage.includes("permission-denied")
      ) {
        errorMessage =
          "Authentication was rejected. Please ensure you're using a valid Telegram account and try again.";
      }

      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Show error state if there's a Telegram initialization error
  if (telegramError) {
    return (
      <div className="text-center p-4">
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 font-medium mb-2">
            ❌ Telegram Initialization Error
          </p>
          <p className="text-red-700 text-sm mb-3">{telegramError}</p>
          <button
            onClick={retry}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
          >
            Retry Initialization
          </button>
        </div>
      </div>
    );
  }

  if (!isInTelegram) {
    return (
      <div className="text-center p-4">
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 font-medium mb-2">
            ⚠️ Not in Telegram Environment
          </p>
          <p className="text-yellow-700 text-sm">
            This app is designed to work within Telegram Web App.
            <br />
            Please open it from a Telegram bot or mini app to use Telegram
            authentication.
          </p>
        </div>
        <button
          disabled
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gray-300 text-gray-500 h-10 px-4 py-2"
        >
          Sign in with Telegram (Unavailable)
        </button>
      </div>
    );
  }

  return (
    <div className="text-center p-4">
      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <p className="text-green-800 font-medium mb-1">
          ✅ Telegram Environment Detected
        </p>
        <p className="text-green-700 text-sm">
          You can now authenticate with your Telegram account.
        </p>
      </div>

      <button
        onClick={handleTelegramAuth}
        disabled={isLoading}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
      >
        {isLoading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Signing in...
          </>
        ) : (
          "Sign in with Telegram"
        )}
      </button>

      <p className="text-xs text-gray-500 mt-3">
        Note: This requires the Firebase Functions to be properly configured
        with your Telegram bot token.
      </p>
    </div>
  );
};

export default TelegramAuth;
