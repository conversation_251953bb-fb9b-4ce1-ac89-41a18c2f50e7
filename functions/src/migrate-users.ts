import * as admin from "firebase-admin";
import { extractRawTonAddress } from "./utils";
import { UserEntity } from "./types";

export async function migrateUsersWithRawTonAddress(): Promise<void> {
  const db = admin.firestore();
  const usersRef = db.collection("users");

  console.log("Starting migration to add raw_ton_wallet_address field...");

  try {
    const snapshot = await usersRef.where("ton_wallet_address", "!=", "").get();

    if (snapshot.empty) {
      console.log("No users found with TON wallet addresses");
      return;
    }

    console.log(
      `Found ${snapshot.docs.length} users with TON wallet addresses`
    );

    const batch = db.batch();
    let updateCount = 0;

    for (const doc of snapshot.docs) {
      const userData = doc.data() as UserEntity;

      if (userData.raw_ton_wallet_address) {
        console.log(
          `User ${doc.id} already has raw_ton_wallet_address, skipping`
        );
        continue;
      }

      if (userData.ton_wallet_address) {
        const rawAddress = extractRawTonAddress(userData.ton_wallet_address);

        if (rawAddress) {
          batch.update(doc.ref, {
            raw_ton_wallet_address: rawAddress,
          });
          updateCount++;
          console.log(
            `Will update user ${doc.id}: ${userData.ton_wallet_address} -> ${rawAddress}`
          );
        } else {
          console.warn(
            `Invalid TON address format for user ${doc.id}: ${userData.ton_wallet_address}`
          );
        }
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      console.log(
        `Successfully updated ${updateCount} users with raw_ton_wallet_address field`
      );
    } else {
      console.log("No users needed updating");
    }
  } catch (error) {
    console.error("Error during migration:", error);
    throw error;
  }
}

export const runUserMigration = async (): Promise<void> => {
  try {
    await migrateUsersWithRawTonAddress();
    console.log("Migration completed successfully");
  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
};
