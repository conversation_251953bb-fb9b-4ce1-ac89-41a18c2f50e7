import { Timestamp } from "firebase-admin/firestore";

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role: "user" | "admin";
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string; // Raw address without EQ/UQ prefix and checksum
  balance?: UserBalance;
  createdAt: Timestamp;
}

export interface ProductEntity {
  id: string;
  title: string;
  description: string;
  price: number;
  sellerId: string;
  category: string;
  imageUrls: string[];
  status: "active" | "sold" | "inactive";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface OrderEntity {
  id: string;
  buyerId: string;
  sellerId: string;
  productId: string;
  amount: number;
  status: "pending" | "confirmed" | "completed" | "cancelled";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TxLookup {
  id: string;
  last_checked_record_id: string;
  updatedAt: Timestamp;
}
