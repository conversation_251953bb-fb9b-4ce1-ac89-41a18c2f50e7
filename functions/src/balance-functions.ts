import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import {
  getUserBalance,
  hasAvailableBalance,
  lockFunds,
  unlockFunds,
  spendLockedFunds,
} from "./balance-service";
import { UserEntity } from "./types";

export const getBalance = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get balance."
    );
  }

  try {
    const balance = await getUserBalance(context.auth.uid);
    return {
      success: true,
      balance,
    };
  } catch (error) {
    console.error("Error getting user balance:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting balance."
    );
  }
});

export const checkPurchaseEligibility = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { amount } = data;

    if (!amount || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Valid amount is required."
      );
    }

    try {
      const db = admin.firestore();
      const userDoc = await db.collection("users").doc(context.auth.uid).get();

      if (!userDoc.exists) {
        throw new functions.https.HttpsError("not-found", "User not found.");
      }

      const user = userDoc.data() as UserEntity;

      if (!user.tg_id) {
        return {
          eligible: false,
          reason: "Telegram ID required",
        };
      }

      if (!user.ton_wallet_address) {
        return {
          eligible: false,
          reason: "TON wallet connection required",
        };
      }

      const hasBalance = await hasAvailableBalance(context.auth.uid, amount);

      return {
        eligible: hasBalance,
        reason: hasBalance ? null : "Insufficient balance",
      };
    } catch (error) {
      console.error("Error checking purchase eligibility:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while checking eligibility."
      );
    }
  }
);

export const lockOrderFunds = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { amount, orderId } = data;

  if (!amount || amount <= 0) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Valid amount is required."
    );
  }

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const hasBalance = await hasAvailableBalance(context.auth.uid, amount);

    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Insufficient available balance."
      );
    }

    const newBalance = await lockFunds(context.auth.uid, amount);

    return {
      success: true,
      balance: newBalance,
      message: `Locked ${amount} TON for order ${orderId}`,
    };
  } catch (error) {
    console.error("Error locking funds:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while locking funds."
    );
  }
});

export const unlockOrderFunds = functions.https.onCall(
  async (data, context) => {
    // Ensure the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { amount, orderId } = data;

    if (!amount || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Valid amount is required."
      );
    }

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const newBalance = await unlockFunds(context.auth.uid, amount);

      return {
        success: true,
        balance: newBalance,
        message: `Unlocked ${amount} TON from order ${orderId}`,
      };
    } catch (error) {
      console.error("Error unlocking funds:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while unlocking funds."
      );
    }
  }
);

export const completeOrder = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { amount, orderId } = data;

  if (!amount || amount <= 0) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Valid amount is required."
    );
  }

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const newBalance = await spendLockedFunds(context.auth.uid, amount);

    return {
      success: true,
      balance: newBalance,
      message: `Completed order ${orderId} - spent ${amount} TON`,
    };
  } catch (error) {
    console.error("Error completing order:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while completing order."
    );
  }
});
