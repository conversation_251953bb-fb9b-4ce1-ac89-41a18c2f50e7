import * as functions from "firebase-functions";

export interface AppConfig {
  app: {
    environment: string;
    project_id: string;
  };
  telegram: {
    bot_token: string;
  };
  ton: {
    rpc_url_testnet?: string;
    rpc_url_mainnet?: string;
    marketplace_wallet: string;
    network: string;
    api_key?: string;
  };
  firebase?: {
    service_account_key?: string;
  };
}

let cachedConfig: AppConfig | null = null;

export function getConfig(): AppConfig {
  if (cachedConfig) {
    return cachedConfig;
  }

  const config = functions.config();

  cachedConfig = {
    app: {
      environment: config.app?.environment ?? "development",
      project_id: config.app?.project_id ?? "",
    },
    telegram: {
      bot_token: config.telegram?.bot_token ?? "",
    },
    ton: {
      rpc_url_testnet: config.ton?.rpc_url_testnet,
      rpc_url_mainnet: config.ton?.rpc_url_mainnet,
      marketplace_wallet: config.ton?.marketplace_wallet ?? "",
      network: config.ton?.network ?? "mainnet",
      api_key: config.ton?.api_key,
    },
    firebase: {
      service_account_key: config.firebase?.service_account_key,
    },
  };

  return cachedConfig;
}

export function getTonRpcUrl(): string {
  const config = getConfig();
  const network = config.ton.network;

  let rpcUrl: string;
  if (network === "testnet") {
    rpcUrl =
      config.ton.rpc_url_testnet ?? "https://testnet.toncenter.com/api/v2/";
  } else {
    rpcUrl =
      config.ton.rpc_url_mainnet ??
      "https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c";
  }

  if (!rpcUrl) {
    throw new Error(`TON RPC URL for ${network} network not configured`);
  }

  // Ensure URL ends with / for proper path concatenation
  return rpcUrl.endsWith("/") ? rpcUrl : `${rpcUrl}/`;
}

export function getMarketplaceWallet(): string {
  const config = getConfig();
  const wallet = config.ton.marketplace_wallet;

  if (!wallet) {
    throw new Error("TON marketplace wallet not configured");
  }

  return wallet;
}

export function getTelegramBotToken(): string {
  const config = getConfig();
  const token = config.telegram.bot_token;

  if (!token) {
    throw new Error("Telegram bot token not configured");
  }

  return token;
}

export function isDevelopment(): boolean {
  const config = getConfig();
  return config.app.environment !== "production";
}
